"use client"

import { useState, useEffect, useCallback, useRef } from "react"

/**
 * 防抖 Hook
 */
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)
  const previousValueRef = useRef<T>(value)

  useEffect(() => {
    // 如果值没有变化，不触发防抖
    if (value === previousValueRef.current) {
      return
    }

    previousValueRef.current = value

    const timer = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(timer)
    }
  }, [value, delay])

  return debouncedValue
}

/**
 * 节流 Hook
 */
function useThrottle<T extends (...args: any[]) => any>(fn: T, delay: number): T {
  const lastCallRef = useRef<number>(0)

  return useCallback((...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastCallRef.current >= delay) {
      lastCallRef.current = now
      return fn(...args)
    }
  }, [fn, delay]) as T
}

/**
 * 分页搜索参数接口
 */
export interface PaginatedSearchParams {
  pageNumber?: number
  pageSize?: number
  keyword?: string
  [key: string]: any
}

/**
 * 分页搜索响应接口
 */
export interface PaginatedSearchResponse<T> {
  records: T[]
  total: number
  [key: string]: any
}

/**
 * 分页搜索配置接口
 */
export interface UsePaginatedSearchConfig<T> {
  /** 搜索函数 */
  searchFn: (params: PaginatedSearchParams) => Promise<PaginatedSearchResponse<T>>
  /** 每页大小，默认 10 */
  pageSize?: number
  /** 防抖延迟，默认 300ms */
  debounceDelay?: number
  /** 是否启用，默认 true */
  enabled?: boolean
  /** 额外的搜索参数 */
  extraParams?: Record<string, any>
}

/**
 * 分页搜索 Hook 返回值接口
 */
export interface UsePaginatedSearchReturn<T> {
  /** 数据列表 */
  data: T[]
  /** 是否加载中 */
  isLoading: boolean
  /** 是否还有更多数据 */
  hasMore: boolean
  /** 搜索关键词 */
  searchTerm: string
  /** 设置搜索关键词 */
  setSearchTerm: (term: string) => void
  /** 加载更多数据 */
  loadMore: () => void
  /** 重置数据 */
  reset: () => void
  /** 刷新数据 */
  refresh: () => void
  /** 处理滚动事件 */
  handleScroll: (e: React.UIEvent<HTMLDivElement>) => void
  /** 总数据量 */
  total: number
  /** 当前页码 */
  pageNumber: number
}

/**
 * 分页搜索 Hook
 *
 * @param config 配置参数
 * @returns 分页搜索状态和方法
 */
export function usePaginatedSearch<T>({
  searchFn,
  pageSize = 10,
  debounceDelay = 300,
  enabled = true,
  extraParams = {}
}: UsePaginatedSearchConfig<T>): UsePaginatedSearchReturn<T> {
  const [data, setData] = useState<T[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [pageNumber, setPageNumber] = useState(1)
  const [total, setTotal] = useState(0)

  // 使用防抖处理搜索关键词
  const debouncedSearchTerm = useDebounce(searchTerm, debounceDelay)

  // 请求状态引用 - 用于防止重复请求
  const requestRef = useRef<{
    isRequesting: boolean
    lastKeyword: string
    lastPage: number
  }>({
    isRequesting: false,
    lastKeyword: "",
    lastPage: 0
  })

  /**
   * 获取数据
   */
  const fetchData = useCallback(async (
    page: number,
    keyword: string,
    reset: boolean = false
  ) => {
    if (!enabled) return

    // 如果已经在请求中，直接返回
    if (requestRef.current.isRequesting) {
      return
    }

    // 如果是相同的请求参数且不是强制刷新，直接返回
    if (!reset &&
        page === requestRef.current.lastPage &&
        keyword === requestRef.current.lastKeyword) {
      return
    }

    // 标记请求状态
    requestRef.current.isRequesting = true

    // 设置加载状态
    if (page === 1 || reset) {
      setIsLoading(true)
    }

    try {
      const params: PaginatedSearchParams = {
        pageNumber: page,
        pageSize,
        keyword: keyword || undefined,
        ...extraParams
      }

      const response = await searchFn(params)

      // 更新请求参数记录
      requestRef.current.lastPage = page
      requestRef.current.lastKeyword = keyword

      if (response && response.records && Array.isArray(response.records)) {
        // 更新数据列表
        if (reset || page === 1) {
          setData(response.records)
          setPageNumber(1)
        } else {
          setData(prev => [...prev, ...response.records])
          setPageNumber(page)
        }

        // 更新总数
        setTotal(response.total || 0)

        // 判断是否还有更多数据
        const currentCount = page * pageSize
        setHasMore(currentCount < (response.total || 0))
      } else {
        // 处理空响应
        if (reset || page === 1) {
          setData([])
          setPageNumber(1)
        }
        setHasMore(false)
        setTotal(0)
      }
    } catch (error) {
      console.error("获取数据失败:", error)
      if (reset || page === 1) {
        setData([])
        setTotal(0)
        setPageNumber(1)
      }
      setHasMore(false)
    } finally {
      setIsLoading(false)
      requestRef.current.isRequesting = false
    }
  }, [searchFn, pageSize, enabled, extraParams])

  /**
   * 加载更多数据
   */
  const loadMore = useCallback(() => {
    if (!isLoading && hasMore && !requestRef.current.isRequesting) {
      const nextPage = pageNumber + 1
      fetchData(nextPage, debouncedSearchTerm, false)
    }
  }, [fetchData, isLoading, hasMore, pageNumber, debouncedSearchTerm])

  /**
   * 重置数据
   */
  const reset = useCallback(() => {
    setData([])
    setPageNumber(1)
    setHasMore(true)
    setTotal(0)
    requestRef.current = {
      isRequesting: false,
      lastKeyword: "",
      lastPage: 0
    }
  }, [])

  /**
   * 刷新数据
   */
  const refresh = useCallback(() => {
    setPageNumber(1)
    fetchData(1, debouncedSearchTerm, true)
  }, [fetchData, debouncedSearchTerm])

  /**
   * 处理滚动事件（内部函数）
   */
  const handleScrollInternal = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget
    // 当滚动到距离底部 50px 时加载更多
    if (scrollHeight - scrollTop <= clientHeight + 50 && !isLoading && hasMore && !requestRef.current.isRequesting) {
      loadMore()
    }
  }, [loadMore, isLoading, hasMore])

  /**
   * 处理滚动事件（节流版本）
   */
  const handleScroll = useThrottle(handleScrollInternal, 200)

  // 当防抖后的搜索词变化时重新获取数据
  useEffect(() => {
    if (enabled &&
        debouncedSearchTerm !== requestRef.current.lastKeyword &&
        !requestRef.current.isRequesting) {
      setPageNumber(1)
      fetchData(1, debouncedSearchTerm, true)
    }
  }, [debouncedSearchTerm, enabled, fetchData])

  // 初始化数据加载
  useEffect(() => {
    if (enabled &&
        data.length === 0 &&
        !requestRef.current.isRequesting &&
        requestRef.current.lastKeyword === "" &&
        requestRef.current.lastPage === 0) {
      fetchData(1, "", true)
    }
  }, [enabled, fetchData, data.length])

  return {
    data,
    isLoading,
    hasMore,
    searchTerm,
    setSearchTerm,
    loadMore,
    reset,
    refresh,
    handleScroll,
    total,
    pageNumber
  }
}
