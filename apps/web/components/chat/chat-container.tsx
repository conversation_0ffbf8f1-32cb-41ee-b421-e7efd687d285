"use client"

import { useState, useRef, useEffect, useMemo } from "react"
import { ChatMessage, type MessageRole } from "./chat-message"
import { ChatInput } from "./chat-input"
import { cn } from "@ragtop-web/ui/lib/utils"
import { Conversation } from "@/service/session-service"
import { useToast } from "@ragtop-web/ui/components/use-toast"
import { useSendMessageStream, useChatMessageState } from "@/hooks/use-chat-stream"
import { deduplicateArray } from "@/lib/chat-utils"

interface ChatContainerProps {
  initialMessages?: Conversation[]
  className?: string
  agentName?: string
  sessionId?: string
  agentId?: string
  isLoadingHistory?: boolean
}

export function ChatContainer({
  initialMessages = [],
  className,
  agentName = "助手",
  sessionId,
  agentId,
  isLoadingHistory = false
}: ChatContainerProps) {
  const [messages, setMessages] = useState<Conversation[]>(initialMessages)
    const [tempMessage, setTempMessage] = useState<Conversation[]>([])

  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const { toast } = useToast()
  const { sendMessage } = useSendMessageStream();
  const {
    message,
    isStreaming,
    questionId,
    error,
    startStream,
    cancelStream,
    clearMessage,
  } = useChatMessageState(sendMessage);

  console.log(tempMessage,message)

  const finalMessage = useMemo(()=>{
    return [...messages, ...tempMessage]
  },[messages, tempMessage])
  // 同步 initialMessages
  useEffect(() => {
    setMessages(initialMessages)
  }, [initialMessages])


  useEffect(() => {
    setTempMessage((prev) =>
      prev.map((msg) =>
        msg.id === "template-question" ? { ...msg, id: questionId } : msg
      )
    );
  }, [questionId]);

    // 监听 message.content 变化并更新 tempMessage 中对应的项
  useEffect(() => {
    if(message?.id){
       setTempMessage((prev) => {
      return deduplicateArray([...prev,message], message.id)
    }
    );
    }

  }, [message?.content,message?.id]);


  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current
      scrollContainer.scrollTop = scrollContainer.scrollHeight
    }
  }, [messages])


  const handleSendMessage = async (content: string) => {
    // 检查必要参数
    if (!sessionId || !agentId) {
      toast({
        title: "错误",
        description: "缺少会话或代理信息",
        variant: "destructive",
      })
      return
    }

    // Add user message
    const userMessage: Conversation = {
      id: `template-question`,
      role: "user",
      content,
    }

    setTempMessage((prev) => [...prev, userMessage])

    // 发送流式消息
    await startStream(
      {
        agent_id: agentId,
        session_id: sessionId,
        text_content: content,
      },
    )
  }

  return (
    <div className={cn("flex flex-col h-full", className)}>
      <div
        className="flex-1 overflow-y-auto"
        ref={scrollAreaRef}
      >
        {isLoadingHistory ? (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
              <span>加载历史消息...</span>
            </div>
          </div>
        ) : finalMessage.length === 0 ? (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            开始新的对话...
          </div>
        ) : (
          <div>
            {finalMessage?.map((message) => (
              <ChatMessage
                key={message.id}
                role={message.role}
                content={message.content}
                agentName={agentName}
                references={message.references}
                files={message.files}
              />
            ))}
            {isStreaming && (
              <div className="flex items-center space-x-2 p-4 text-muted-foreground">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                <span>正在生成回复...</span>
              </div>
            )}
          </div>
        )}
      </div>

      <div className="mt-auto pt-4">
        <ChatInput
          onSend={handleSendMessage}
          disabled={isStreaming || isLoadingHistory}
        />
      </div>
    </div>
  )
}
