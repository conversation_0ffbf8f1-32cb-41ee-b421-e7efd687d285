"use client"

import { useState } from "react"
import { Button } from "@ragtop-web/ui/components/button"
import { Textarea } from "@ragtop-web/ui/components/textarea"
import { Send } from "lucide-react"
import { cn } from "@ragtop-web/ui/lib/utils"

interface ChatInputProps {
  onSend: (message: string) => void
  className?: string
  placeholder?: string
  disabled?: boolean
}

export function ChatInput({
  onSend,
  className,
  placeholder = "请输入您的问题......",
  disabled = false
}: ChatInputProps) {
  const [message, setMessage] = useState("")

  const handleSend = () => {
    if (message.trim() && !disabled) {
      onSend(message)
      setMessage("")
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey && !disabled) {
      e.preventDefault()
      handleSend()
    }
  }

  return (
    <div className={cn("border rounded-lg p-2", className)}>
      <Textarea
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder={disabled ? "正在处理中..." : placeholder}
        disabled={disabled}
        className="min-h-[60px] rounded-none border-0 border-b-1 focus-visible:ring-0 resize-none shadow-none"
      />

      <div className="flex items-center justify-end mt-2">
        <Button
          size="sm"
          className="rounded-full px-3 flex items-center"
          onClick={handleSend}
          disabled={disabled || !message.trim()}
        >
          <span className="mr-1">Send</span>
          <Send className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
}
