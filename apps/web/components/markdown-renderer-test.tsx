"use client"

import { MarkdownRenderer } from './markdown-renderer'

/**
 * 测试 MarkdownRenderer 组件的引用功能
 */
export function MarkdownRendererTest() {
  // 模拟包含引用的内容
  const testContent = `
这是一个测试内容，包含引用 ##0$$ 和另一个引用 ##1$$。

这里是一些普通文本，然后是第三个引用 ##2$$。

## 标题测试

这是一个代码块：

\`\`\`javascript
console.log("Hello World");
\`\`\`

最后一个引用 ##3$$。
  `;

  // 模拟引用数据
  const testReferences = [
    {
      content: "这是第一个引用的内容，来自某个文档。",
      document_name: "文档1.pdf",
      document_id: "doc1"
    },
    {
      content: "这是第二个引用的内容，包含更多详细信息。",
      document_name: "文档2.docx",
      document_id: "doc2"
    },
    {
      content: "第三个引用的内容，来自另一个来源。",
      document_name: "文档3.txt",
      document_id: "doc3"
    },
    {
      content: "最后一个引用的内容。",
      document_name: "文档4.pdf",
      document_id: "doc4"
    }
  ];

  const testFiles = [];

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">MarkdownRenderer 引用功能测试</h1>
      
      <div className="border rounded-lg p-4 bg-card">
        <h2 className="text-lg font-semibold mb-2">测试内容：</h2>
        <MarkdownRenderer 
          content={testContent}
          references={testReferences}
          files={testFiles}
        />
      </div>

      <div className="mt-6">
        <h2 className="text-lg font-semibold mb-2">说明：</h2>
        <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
          <li>点击数字按钮可以查看引用内容</li>
          <li>引用格式为 ##数字$$</li>
          <li>Popover 会显示引用的详细信息</li>
        </ul>
      </div>
    </div>
  );
}
