"use client"

import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeHighlight from 'rehype-highlight'
import { cn } from '@ragtop-web/ui/lib/utils'
import { useCallback } from 'react'
import { Button } from '@ragtop-web/ui/components/button'
import { Popover, PopoverContent, PopoverTrigger } from '@ragtop-web/ui/components/popover'
import { FileText } from 'lucide-react'

// 注意：如果样式导入有问题，可以注释掉下面这行
// import 'highlight.js/styles/github.css'

interface MarkdownRendererProps {
  content: string
  className?: string
  references?: any[]
  files?: any[]
}

// Preprocess LaTeX equations to be rendered by KaTeX
// ref: https://github.com/remarkjs/react-markdown/issues/785

export const preprocessLaTeX = (content: string) => {
  const blockProcessedContent = content.replace(
    /\\\[([\s\S]*?)\\\]/g,
    (_, equation) => `$$${equation}$$`,
  );
  const inlineProcessedContent = blockProcessedContent.replace(
    /\\\(([\s\S]*?)\\\)/g,
    (_, equation) => `$${equation}$`,
  );
  return inlineProcessedContent;
};

// ragflow代码中包含了，暂时不知道什么意思
export function replaceThinkToSection(text: string = '') {
  const pattern = /<think>([\s\S]*?)<\/think>/g;

  const result = text.replace(pattern, '<section class="think">$1</section>');

  return result;
}

/**
 * 处理引用格式 ##3$$ 的正则表达式
 */
const referenceRegex = /##(\d+)\$\$/g;

/**
 * 获取引用索引
 */
const getReferenceIndex = (match: string) => {
  const indexMatch = match.match(/##(\d+)\$\$/);
  return indexMatch && indexMatch[1] ? parseInt(indexMatch[1], 10) : -1;
};


/**
 * Markdown 渲染器组件
 *
 * 支持 GitHub Flavored Markdown 和代码高亮
 */
export function MarkdownRenderer({ content, className, references = [], files = [] }: MarkdownRendererProps) {

  /**
   * 获取引用信息
   */
  const getReferenceInfo = useCallback((index: number) => {
    if (!references || !references[index]) {
      return null;
    }
    return references[index];
  }, [references]);

  /**
   * 获取 Popover 内容
   */
  const getPopoverContent = useCallback((index: number) => {
    const referenceInfo = getReferenceInfo(index);
    if (!referenceInfo) {
      return <div className="p-2 text-sm text-muted-foreground">引用信息不可用</div>;
    }

    return (
      <div className="p-3 max-w-sm">
        <div className="flex items-center gap-2 mb-2">
          <FileText className="h-4 w-4" />
          <span className="font-medium text-sm">引用内容</span>
        </div>
        <div className="text-sm text-muted-foreground">
          {referenceInfo.content || referenceInfo.document_name || '引用内容'}
        </div>
      </div>
    );
  }, [getReferenceInfo]);

  /**
   * 自定义文本渲染器，处理引用格式
   */
  const renderTextWithReferences = useCallback((text: string) => {
    const parts = [];
    let lastIndex = 0;
    let match;

    // 重置正则表达式的 lastIndex
    referenceRegex.lastIndex = 0;

    while ((match = referenceRegex.exec(text)) !== null) {
      // 添加引用前的文本
      if (match.index > lastIndex) {
        parts.push(text.slice(lastIndex, match.index));
      }

      // 获取引用索引
      const index = getReferenceIndex(match[0]);

      // 创建引用按钮
      parts.push(
        <Popover key={`ref-${index}-${match.index}`}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="h-6 px-2 mx-1 text-xs bg-primary/10 hover:bg-primary/20 border-primary/30"
            >
              {index + 1}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-80">
            {getPopoverContent(index)}
          </PopoverContent>
        </Popover>
      );

      lastIndex = match.index + match[0].length;
    }

    // 添加剩余的文本
    if (lastIndex < text.length) {
      parts.push(text.slice(lastIndex));
    }

    return parts.length > 1 ? parts : text;
  }, [getPopoverContent]);

  return (
    <div className={cn("prose prose-sm dark:prose-invert max-w-none", className)}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeHighlight]}
        components={{
          // 自定义代码块样式
          code: ({ node, className, children, ...props }: any) => {
            const inline = !className?.includes('language-')
            const match = /language-(\w+)/.exec(className || '')
            return !inline && match ? (
              <pre className="bg-muted/50 border rounded-md p-4 overflow-x-auto my-4">
                <code className={cn("text-sm font-mono", className)} {...props}>
                  {children}
                </code>
              </pre>
            ) : (
              <code className="bg-muted/50 px-1.5 py-0.5 rounded text-sm font-mono border" {...props}>
                {children}
              </code>
            )
          },
          // 自定义表格样式
          table: ({ children }) => (
            <div className="overflow-x-auto">
              <table className="min-w-full border-collapse border border-border">
                {children}
              </table>
            </div>
          ),
          th: ({ children }) => (
            <th className="border border-border bg-muted px-4 py-2 text-left font-medium">
              {children}
            </th>
          ),
          td: ({ children }) => (
            <td className="border border-border px-4 py-2">
              {children}
            </td>
          ),
          // 自定义链接样式
          a: ({ children, href }) => (
            <a
              href={href}
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary hover:underline"
            >
              {children}
            </a>
          ),
          // 自定义引用块样式
          blockquote: ({ children }) => (
            <blockquote className="border-l-4 border-primary pl-4 italic text-muted-foreground">
              {children}
            </blockquote>
          ),
          // 自定义列表样式
          ul: ({ children }) => (
            <ul className="list-disc list-inside space-y-1">
              {children}
            </ul>
          ),
          ol: ({ children }) => (
            <ol className="list-decimal list-inside space-y-1">
              {children}
            </ol>
          ),
          // 自定义标题样式
          h1: ({ children }) => (
            <h1 className="text-2xl font-bold mb-4 mt-6 first:mt-0">
              {children}
            </h1>
          ),
          h2: ({ children }) => (
            <h2 className="text-xl font-semibold mb-3 mt-5 first:mt-0">
              {children}
            </h2>
          ),
          h3: ({ children }) => (
            <h3 className="text-lg font-medium mb-2 mt-4 first:mt-0">
              {children}
            </h3>
          ),
          // 自定义段落样式，处理引用
          p: ({ children }) => {
            // 如果 children 是字符串，处理引用格式
            if (typeof children === 'string') {
              const processedChildren = renderTextWithReferences(children);
              return (
                <p className="mb-3 leading-relaxed">
                  {processedChildren}
                </p>
              );
            }

            // 如果 children 是数组，递归处理每个元素
            if (Array.isArray(children)) {
              const processedChildren = children.map((child) => {
                if (typeof child === 'string') {
                  return renderTextWithReferences(child);
                }
                return child;
              });

              return (
                <p className="mb-3 leading-relaxed">
                  {processedChildren}
                </p>
              );
            }

            return (
              <p className="mb-3 leading-relaxed">
                {children}
              </p>
            );
          },
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  )
}
