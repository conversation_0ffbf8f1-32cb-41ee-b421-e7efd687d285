"use client"

import { useState } from "react"
import { Trash2 } from "lucide-react"
import Link from "next/link"
import { But<PERSON> } from "@ragtop-web/ui/components/button"
import {
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@ragtop-web/ui/components/sidebar"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@ragtop-web/ui/components/alert-dialog"
import { useSessions, useDeleteSession, Sessions } from "@/service/session-service"
import { useToast } from "@ragtop-web/ui/components/use-toast"

interface SessionListProps {
  agentId: string
  isExpanded: boolean
}

/**
 * Session列表组件
 * 显示指定Agent下的所有sessions
 */
export function SessionList({ agentId, isExpanded }: SessionListProps) {
  const { toast } = useToast()
  const [sessionToDelete, setSessionToDelete] = useState<Sessions | null>(null)
  
  // 获取sessions数据
  const { data: sessionsData, isLoading } = useSessions(agentId, 1, 20)
  const sessions = sessionsData?.records || []
  
  // 删除session hook
  const deleteSession = useDeleteSession()

  // 处理删除session
  const handleDeleteSession = async (session: Sessions) => {
    try {
      await deleteSession.mutateAsync({ session_id: session.id })
      toast({
        title: "成功",
        description: "会话删除成功",
      })
      setSessionToDelete(null)
    } catch (error) {
      console.error("删除会话失败:", error)
      toast({
        title: "错误",
        description: "删除会话失败",
        variant: "destructive",
      })
    }
  }

  // 如果没有展开，不渲染
  if (!isExpanded) {
    return null
  }

  // 如果正在加载
  if (isLoading) {
    return (
      <SidebarMenuSub>
        <SidebarMenuSubItem>
          <SidebarMenuSubButton>
            <span className="text-muted-foreground text-sm">加载中...</span>
          </SidebarMenuSubButton>
        </SidebarMenuSubItem>
      </SidebarMenuSub>
    )
  }

  // 如果没有sessions
  if (sessions.length === 0) {
    return (
      <SidebarMenuSub>
        <SidebarMenuSubItem>
          <SidebarMenuSubButton>
            <span className="text-muted-foreground text-sm">暂无会话</span>
          </SidebarMenuSubButton>
        </SidebarMenuSubItem>
      </SidebarMenuSub>
    )
  }

  return (
    <>
      <SidebarMenuSub>
        {sessions.map((session) => (
          <SidebarMenuSubItem key={session.id} className="group/sub-item relative">
            <SidebarMenuSubButton asChild>
              <Link href={`/agent/${agentId}/${session.id}`}>
                <span className="truncate">{session.title}</span>
              </Link>
            </SidebarMenuSubButton>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-2 top-1/2 -translate-y-1/2 opacity-0 group-hover/sub-item:opacity-100 transition-opacity h-6 w-6 p-0.5 text-destructive hover:bg-destructive/10 hover:text-destructive"
                  onClick={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    setSessionToDelete(session)
                  }}
                  aria-label={`删除 ${session.title}`}
                >
                  <Trash2 className="h-3.5 w-3.5" />
                  <span className="sr-only">删除 {session.title}</span>
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>确认删除会话</AlertDialogTitle>
                  <AlertDialogDescription>
                    您确定要删除会话 "{session.title}" 吗？此操作无法撤销，会话中的所有消息都将被永久删除。
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel onClick={() => setSessionToDelete(null)}>
                    取消
                  </AlertDialogCancel>
                  <AlertDialogAction 
                    onClick={() => sessionToDelete && handleDeleteSession(sessionToDelete)}
                    disabled={deleteSession.isPending}
                  >
                    {deleteSession.isPending ? "删除中..." : "确认删除"}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </SidebarMenuSubItem>
        ))}
      </SidebarMenuSub>
    </>
  )
}
