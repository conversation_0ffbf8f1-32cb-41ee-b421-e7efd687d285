

"use client"
import { useState } from "react"
import {
  Search,
  FileText,
  PlusIcon,
  Trash2,
  Set<PERSON>s,
  Play,
  Square,
  RefreshCw
} from "lucide-react"
import { DataTable, type ColumnDef } from "@ragtop-web/ui/components/data-table"
import { Input } from "@ragtop-web/ui/components/input"
import { <PERSON><PERSON> } from "@ragtop-web/ui/components/button"
import { Badge } from "@ragtop-web/ui/components/badge"
import { Checkbox } from "@ragtop-web/ui/components/checkbox"
import { CustomContainer } from "@ragtop-web/ui/components/custom-container"
import { Switch } from "@ragtop-web/ui/components/switch"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@ragtop-web/ui/components/alert-dialog"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  <PERSON>ltipProvider,
  TooltipTrigger,
} from "@ragtop-web/ui/components/tooltip"
import { formatDate } from "@/lib/utils"
import type { ReactElement } from "react"
import { ChunkMethodModal } from "../components/chunk-method-modal"
import { sliceMethodOptions } from "../components/slice-method-description"
import {
  type KnowledgeBaseDocList,
  RunStatus,
  useKnowledgeBaseDoc,
  useKBaseDocEnable,
  useKBaseDocDisabled,
  useKBaseDocStartParse,
  useKBaseDocStopParse,
  useKBaseDocModifyChunking,
  useKBaseDocDelete
} from "@/service/knowledge-base-doc-service"
import { useKnowledgeBase, useAddKnowledgeBaseDocuments, SliceMethodType } from "@/service/knowledge-base-service"
import { useToast } from "@ragtop-web/ui/components/use-toast"
import { Pagination } from "@ragtop-web/ui/components/pagination"
import { FileSelectionDrawer } from "@/app/(basic)/knowledge-base/components/file-selection-drawer"

// 解析状态类型
export type ParseStatus = RunStatus


// 渲染状态图标
function renderStatusBadge(status: string): ReactElement {
  switch (status) {
    case RunStatus.Done:
      return <Badge variant="default" className="bg-green-50 text-green-700 border-green-200">解析成功</Badge>
    case RunStatus.Fail:
      return <Badge variant="destructive">解析失败</Badge>
    case RunStatus.Running:
      return <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">解析中</Badge>
    case RunStatus.Unstart:
      return <Badge variant="outline">未解析</Badge>
    case RunStatus.Cancel:
      return <Badge variant="outline">解析已取消</Badge>
    default:
      return <Badge variant="outline">未知状态</Badge>
  }
}

export default function KnowledgeBaseDetails({ knowledgeId }: {
  knowledgeId: string
}) {
  const [pageNumber, setPageNumber] = useState(1)
  const [pageSize] = useState(10)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedFileIds, setSelectedFileIds] = useState<string[]>([])

  // 切片方法设置模态框状态
  const [isChunkMethodModalOpen, setIsChunkMethodModalOpen] = useState(false)
  const [selectedFileForChunkMethod, setSelectedFileForChunkMethod] = useState<{ id: string, name: string } | null>(null)

  // 删除确认对话框状态
  const [fileToDelete, setFileToDelete] = useState<{ id: string, name: string } | null>(null)

  // 文件选择抽屉状态
  const [isFileSelectionDrawerOpen, setIsFileSelectionDrawerOpen] = useState(false)

  // Toast提示
  const { toast } = useToast()

  // 获取知识库数据
  const { data } = useKnowledgeBaseDoc(knowledgeId, pageNumber, pageSize,)
  const { data: knowledgeBase } = useKnowledgeBase(knowledgeId)

  const totalItems = data?.total || 0
  const kbaseDocList = data?.records || []

  // API hooks
  const enableDocMutation = useKBaseDocEnable()
  const disableDocMutation = useKBaseDocDisabled()
  const startParseMutation = useKBaseDocStartParse()
  const stopParseMutation = useKBaseDocStopParse()
  const modifyChunkingMutation = useKBaseDocModifyChunking()
  const addDocumentsMutation = useAddKnowledgeBaseDocuments()
  const deleteDocumentsMutation = useKBaseDocDelete()


  const getChunkLabel = (value?:SliceMethodType)=>{
   return sliceMethodOptions.find(opt => opt.value === value)?.label || "未设置"
  }

  // 处理分页变化
  const handlePageChange = (page: number) => {
    setPageNumber(page)
  }

  // 过滤文件
  const filteredFiles = kbaseDocList.filter((file: KnowledgeBaseDocList) =>
    file.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // 处理文件选择
  const handleFileSelect = (fileId: string) => {
    if (selectedFileIds.includes(fileId)) {
      setSelectedFileIds(selectedFileIds.filter(id => id !== fileId))
    } else {
      setSelectedFileIds([...selectedFileIds, fileId])
    }
  }

  // 处理全选
  const handleSelectAll = () => {
    if (filteredFiles.length > 0 && selectedFileIds.length === filteredFiles.length) {
      setSelectedFileIds([])
    } else {
      setSelectedFileIds(filteredFiles.map((file: KnowledgeBaseDocList) => file.id))
    }
  }

  // 处理添加新文档
  const handleAddNewDocument = () => {
    setIsFileSelectionDrawerOpen(true)
  }

  // 处理文件选择确认
  const handleFileSelectionConfirm = async (selectedFileIds: string[]) => {
    if (!selectedFileIds.length) return

    try {
      await addDocumentsMutation.mutateAsync({
        kbase_id: knowledgeId,
        file_ids: selectedFileIds
      })

      toast({
        title: "添加成功",
        description: `已成功添加 ${selectedFileIds.length} 个文件到知识库`,
      })

      setIsFileSelectionDrawerOpen(false)
    } catch (error) {
      toast({
        title: "添加失败",
        description: "添加文件时发生错误，请重试",
        variant: "destructive",
      })
    }
  }

  // 处理解析文件
  const handleParseFile = async (fileId: string, currentStatus: ParseStatus) => {
    if (!knowledgeBase) return

    try {
      // 根据当前状态决定操作
      if (currentStatus === RunStatus.Running) {
        // 停止解析
        await stopParseMutation.mutateAsync({
          // kbase_id: knowledgeId,
          document_id: fileId
        })
        toast({
          title: "成功",
          description: "已停止解析文件",
        })
      } else {
        // 开始解析
        await startParseMutation.mutateAsync({
          // kbase_id: knowledgeId,
          document_id: fileId
        })
        toast({
          title: "成功",
          description: "已开始解析文件",
        })
      }

    } catch (error) {
      toast({
        title: "错误",
        description: "操作失败，请重试",
        variant: "destructive",
      })
    }
  }

  // 处理切换文件启用状态
  const handleToggleEnabled = async (fileId: string, currentEnabled: boolean) => {
    if (!knowledgeBase) return

    try {
      if (currentEnabled) {
        // 禁用文档
        await disableDocMutation.mutateAsync({
          // kbase_id: knowledgeId,
          document_id: fileId
        })
        toast({
          title: "成功",
          description: "已禁用文档",
        })
      } else {
        // 启用文档
        await enableDocMutation.mutateAsync({
          // kbase_id: knowledgeId,
          document_id: fileId
        })
        toast({
          title: "成功",
          description: "已启用文档",
        })
      }

    } catch (error) {
      toast({
        title: "错误",
        description: "操作失败，请重试",
        variant: "destructive",
      })
    }
  }

  // 处理设置切片方法
  const handleSetChunkMethod = (fileId: string, fileName: string) => {
    setSelectedFileForChunkMethod({ id: fileId, name: fileName })
    setIsChunkMethodModalOpen(true)
  }

  // 处理保存切片方法
  const handleSaveChunkMethod = async (values: { sliceMethod: string, fileId: string }) => {
    if (!selectedFileForChunkMethod) return
    try {
      modifyChunkingMutation.mutate({
        chunk_config: {
          chunk_provider_id: values.sliceMethod,
          pdf_parser_config: {
            parser_provider_id: "DeepDOC"
          },
        },
        document_id: values.fileId
      }, {
        onSuccess: () => {
          toast({
            title: "成功",
            description: "切片方法设置成功",
          })
        }
      }
      )
    } catch (error) {
      toast({
        title: "错误",
        description: "设置失败，请重试",
        variant: "destructive",
      })
    }
  }

  // 处理删除文件
  const handleDeleteFile = (fileId: string, fileName: string) => {
    setFileToDelete({ id: fileId, name: fileName })
  }

  // 确认删除文件
  const confirmDeleteFile = () => {
    if (!fileToDelete) return
    deleteDocumentsMutation.mutate({
      document_id: fileToDelete.id
    }, {
      onSuccess: () => {
        toast({
          title: "成功",
          description: "已删除文件",
        })
        setFileToDelete(null)
      }
    })

  }

  if (!knowledgeBase) {
    return (
      <CustomContainer title="知识库详情">
        <div className="flex items-center justify-center h-64">
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </CustomContainer>
    )
  }

  // 构建面包屑导航
  const breadcrumbs = [
    { title: "知识库列表", href: "/knowledge-base", isCurrent: false },
    { title: knowledgeBase.name || "知识库详情", href: `/knowledge-base/${knowledgeId}`, isCurrent: true }
  ]

  return (
    <CustomContainer
      title={knowledgeBase.name || "知识库详情"}
      breadcrumbs={breadcrumbs}
    >
      <div className="space-y-6">
        {/* 知识库信息 */}
        <div className="flex items-center gap-6 text-sm text-muted-foreground mb-6 p-4 bg-muted/30 rounded-lg">
          <div className="flex items-center gap-2">
            <span className="font-medium">创建时间:</span>
            {formatDate(new Date())}
          </div>
          <div className="flex items-center gap-2">
            <span className="font-medium">切片方法:</span>
            {getChunkLabel(knowledgeBase?.chunk_config?.chunk_provider_id)}
          </div>
          <div className="flex items-center gap-2">
            <span className="font-medium">文档数量:</span>
            {knowledgeBase.doc_num}
          </div>
        </div>

        {/* 搜索和操作栏 */}
        <div className="flex items-center justify-between">
          <div className="relative w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="搜索文件..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Button onClick={handleAddNewDocument}>
            <PlusIcon className="h-4 w-4" />
            新增文档
          </Button>
        </div>

        {/* 文件列表 */}
        {(() => {
          // 定义表格列
          const columns: ColumnDef<KnowledgeBaseDocList>[] = [
            {
              id: "select",
              header: () => (
                <Checkbox
                  checked={
                    filteredFiles.length > 0 &&
                    selectedFileIds.length === filteredFiles.length
                  }
                  onCheckedChange={handleSelectAll}
                />
              ),
              cell: ({ row }) => (
                <Checkbox
                  checked={selectedFileIds.includes(row.original.id)}
                  onCheckedChange={() => handleFileSelect(row.original.id)}
                />
              ),
              size: 40,
            },
            {
              accessorKey: "name",
              header: "名称",
              cell: ({ row }) => (
                <div className="font-medium flex items-center gap-2">
                  <FileText className="h-4 w-4 text-primary" />
                  {row.original.name}
                </div>
              ),
            },
            {
              accessorKey: "segments",
              header: "分段数",
              cell: () => 0,
            },
            {
              accessorKey: "create_time",
              header: "创建",
              cell: () => formatDate(new Date()),
            },
            {
              accessorKey: "sliceMethod",
              header: "切片方法",
              cell: ({ row }) => {
                const file = row.original;
                const method = file.chunk_config?.chunk_provider_id;
                const option = sliceMethodOptions.find(opt => opt.value === method);
                return option ? option.label : method || "未设置";
              },
            },
            {
              accessorKey: "enable_status",
              header: "启用",
              cell: ({ row }) => {
                const file = row.original;
                const isEnabled = file.enable_status === "ENABLED";
                return (
                  <Switch
                    checked={isEnabled}
                    onCheckedChange={() => handleToggleEnabled(file.id, isEnabled)}
                  />
                );
              },
            },
            {
              accessorKey: "run_status",
              header: "状态",
              cell: ({ row }) => renderStatusBadge(row.original.run_status),
            },
            {
              id: "actions",
              header: () => <div className="text-right">操作</div>,
              cell: ({ row }) => {
                const file = row.original;
                return (
                  <div className="flex items-center justify-end gap-1">
                    <TooltipProvider>
                      {/* 解析按钮 */}
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => handleParseFile(file.id, file.run_status)}
                          >
                            {file.run_status === RunStatus.Running ? (
                              <Square className="h-4 w-4 text-amber-500" />
                            ) : file.run_status === RunStatus.Fail ? (
                              <RefreshCw className="h-4 w-4 text-destructive" />
                            ) : (
                              <Play className="h-4 w-4 text-primary" />
                            )}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          {file.run_status === RunStatus.Running
                            ? "停止解析"
                            : file.run_status === RunStatus.Fail
                              ? "重试解析"
                              : "解析文件"}
                        </TooltipContent>
                      </Tooltip>

                      {/* 切片方法设置按钮 */}
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => handleSetChunkMethod(file.id, file.name)}
                          >
                            <Settings className="h-4 w-4 text-muted-foreground" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          设置切片方法
                        </TooltipContent>
                      </Tooltip>

                      {/* 删除按钮 */}
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => handleDeleteFile(file.id, file.name)}
                          >
                            <Trash2 className="h-4 w-4 text-destructive" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          删除文件
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                );
              },
            },
          ];

          return <DataTable columns={columns} data={kbaseDocList} />;
        })()}

        <div className="w-full flex justify-center mt-4">
          <Pagination
            pageCount={Math.ceil(totalItems / pageSize)}
            currentPage={pageNumber}
            onPageChange={handlePageChange}
          />
        </div>
      </div>

      {/* 切片方法设置模态框 */}
      {selectedFileForChunkMethod && (
        <ChunkMethodModal
          open={isChunkMethodModalOpen}
          onClose={() => setIsChunkMethodModalOpen(false)}
          onSubmit={handleSaveChunkMethod}
          initialMethod={knowledgeBase.chunk_config?.chunk_provider_id || "naive"}
          fileId={selectedFileForChunkMethod.id}
          fileName={selectedFileForChunkMethod.name}
        />
      )}

      {/* 删除确认对话框 */}
      <AlertDialog
        open={!!fileToDelete}
        onOpenChange={(open) => !open && setFileToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除文件</AlertDialogTitle>
            <AlertDialogDescription>
              确定要删除文件 <span className="font-medium">{fileToDelete?.name}</span> 吗？
              此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteFile}
              className="bg-destructive text-destructive-foreground"
            >
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* 文件选择抽屉 */}
      <FileSelectionDrawer
        open={isFileSelectionDrawerOpen}
        onClose={() => setIsFileSelectionDrawerOpen(false)}
        onConfirm={handleFileSelectionConfirm}
        kbaseId={knowledgeId}
        knowledgeBaseName={knowledgeBase?.name || "知识库"}
      />
    </CustomContainer>
  )
}