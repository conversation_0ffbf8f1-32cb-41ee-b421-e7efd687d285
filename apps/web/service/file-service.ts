/**
 * 文件服务
 *
 * 提供文件相关的API请求方法
 */

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  createWebApiClient,
  PaginatedResponse,
} from "@/lib/api/web-api-client";
import { API_PREFIX } from "./index";
import { getTeamId } from "@/store/team-store";

// 创建API客户端
const apiClient = createWebApiClient({
  apiPrefix: `${API_PREFIX}/file`,
});

const apiClientUpload = createWebApiClient({
  apiPrefix: `${API_PREFIX}/file`,
  requireTeamId: false,
});

// 文件接口
export interface FileItem {
  id: string
  name: string
  type: string
  size: number // 文件大小（字节）
  link_num: number // 被链接的知识库数量
  create_time: string // 创建时间
  isFolder: boolean // 是否是文件夹
  parentId: string | null // 父文件夹ID，null表示根目录
  path: string // 完整路径
}

// 知识库引用接口
export interface KnowledgeBaseReference {
  id: string;
  name: string;
}

/**
 * 获取文件列表
 * 分页查询
 */
export const useFiles = ( pageNumber = 1, pageSize = 20,kbase_id?:string, scope?:string) => {
  return useQuery({
    queryKey: ["files", pageNumber, pageSize],
    queryFn: () =>
      apiClient.post<PaginatedResponse<FileItem>>(
        "/query",
        {kbase_id,
          scope},
        {
          isPaginated: true,
          pageNumber,
          pageSize,
        }
      ),
  });
};

/**
 * 获取文件夹内容
 */
export const useFolderContents = () => {
  return useMutation({
    mutationFn: (data: { parentId: string | null }) =>
      apiClient.post<FileItem[]>("/folder-contents", data),
  });
};

/**
 * 上传文件（支持多文件上传）
 * team_id 作为查询参数传递，body 中只包含 FormData
 */
export const useUploadFile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: FormData) => {      // 获取当前团队ID
      const teamId = getTeamId();

      // 构建查询参数
      const queryParams = new URLSearchParams();
      if (teamId) {
        queryParams.append("team_id", teamId);
      }

      // 构建完整的URL
      const url = `/upload${queryParams.toString() ? `?${queryParams.toString()}` : ""}`;

      // 现在 fetch-client 会自动检测 FormData 并正确处理：
      // - 不会对 FormData 进行 JSON.stringify
      // - 不会设置 Content-Type（让浏览器自动设置）
      // - 不会在 body 中添加 team_id（因为我们通过查询参数传递）
      return apiClientUpload.post<FileItem>(url, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["files"] });
    },
  });
};

/**
 * 批量上传文件
 * team_id 作为查询参数传递，body 中只包含 FormData
 */
export const useUploadFiles = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (files: File[]) => {
      // 获取当前团队ID
      const teamId = getTeamId();

      // 构建查询参数
      const queryParams = new URLSearchParams();
      if (teamId) {
        queryParams.append("team_id", teamId);
      }

      // 构建完整的URL
      const url = `/upload${queryParams.toString() ? `?${queryParams.toString()}` : ""}`;

      const uploadPromises = files.map((file) => {
        const formData = new FormData();
        formData.append("file", file);
        // fetch-client 会自动处理 FormData
        return apiClientUpload.post<FileItem>(url, formData);
      });

      return Promise.all(uploadPromises);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["files"] });
    },
  });
};

/**
 * 创建文件夹
 */
export const useCreateFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { name: string; parentId: string | null }) =>
      apiClient.post<FileItem>("/create-folder", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["files"] });
    },
  });
};

/**
 * 删除文件
 */
export const useDeleteFile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { file_id: string }) => apiClient.post("/delete", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["files"] });
    },
  });
};

/**
 * 重命名文件
 */
export const useRenameFile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { file_id: string; name: string }) =>
      apiClient.post<FileItem>("/modify", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["files"] });
    },
  });
};

/**
 * 移动文件
 */
export const useMoveFile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { file_id: string; parentId: string | null }) =>
      apiClient.post<FileItem>("/move", data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["files"] });
    },
  });
};

/**
 * 获取文件的知识库引用
 */
export const useFileReferences = () => {
  return useMutation({
    mutationFn: (data: { file_id: string }) =>
      apiClient.post<KnowledgeBaseReference[]>("/references", data),
  });
};
