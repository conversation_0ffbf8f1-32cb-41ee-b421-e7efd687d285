import { API_PREFIX } from "@/service";
import { useState, useRef, useCallback } from "react";
import { ChatMessage, ContentType, FileContent, ReadyContnet, ReferenceContent } from "@/service/session-service";

export type StreamResponse = {
  id: string;
  model: string;
  created: number;
  choices: {
    delta: {
      content?: string | FileContent | ReferenceContent | ReadyContnet;
    };
    finish_reason: string | null;
    index: number;
  }[];
};

export interface Message {
  id?:string,
  role?:string,
  content?:string | FileContent | ReferenceContent | ReadyContnet
} 

function parseSSEData(
  line: string
): { event?: string; data?: StreamResponse } | null {
  try {
    const trimmed = line.trim();
    if (trimmed.startsWith("event:")) {
      return { event: trimmed.slice(6).trim() };
    }
    if (trimmed.startsWith("data:")) {
      const dataStr = trimmed.slice(5).trim();
      if (dataStr === "[DONE]") return null;
      return { data: JSON.parse(dataStr) };
    }
  } catch (err) {
    console.warn("Failed to parse SSE data line:", line, err);
  }
  return null;
}

type SendMessageFn = (
  data: ChatMessage,
  onChunk: (chunk: StreamResponse, eventType?: string) => void,
  onComplete?: () => void,
  onError?: (error: string) => void
) => AbortController;

export const useChatMessageState = (sendMessage: SendMessageFn) => {
  const [message, setMessage] = useState<Message | undefined>(undefined);
  const [references, setReferences] = useState<StreamResponse[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);
  const [questionId, setQuestionId] = useState<string>("")
  const [error, setError] = useState<string | null>(null);
  const controllerRef = useRef<AbortController | null>(null);

  const startStream = useCallback(
    (data: ChatMessage) => {
      controllerRef.current?.abort();
      setMessage({
        id: `template-answer`,
        role: "assistant",
        content: "",
      });
      setReferences([]);
      setError(null);
      setIsStreaming(true);

      const controller = sendMessage(
        data,
        (chunk: StreamResponse, eventType?: string) => {
          if (eventType === ContentType.Text) {
            const content = chunk.choices?.[0]?.delta?.content;

            if (content) {
              setMessage((message) => ({
                ...message,
                content: message?.content + content,
              }));
            }
          } else if (eventType === ContentType.Ready) {
            const content = chunk.choices?.[0]?.delta?.content;
            setQuestionId(content?.question_id);
            setMessage((message) => ({
              ...message,
              id: content?.answer_id,
            }));
          } else if (eventType === ContentType.Reference) {
            setReferences((prev) => [...prev, chunk]);
          }
        },
        () => setIsStreaming(false),
        (err: string) => {
          setError(err);
          setIsStreaming(false);
        }
      );

      controllerRef.current = controller;
    },
    [sendMessage]
  );

  const cancelStream = useCallback(() => {
    controllerRef.current?.abort();
    setIsStreaming(false);
  }, []);

  const clearMessage = useCallback(() => {
    setMessage({});
    setReferences([]);
    setError(null);
  }, []);

  return {
    message,
    references,
    isStreaming,
    error,
    questionId,
    startStream,
    cancelStream,
    clearMessage,
  };
};

export const useSendMessageStream = () => {
  // const queryClient = useQueryClient();

  const sendMessage = (
    data: ChatMessage,
    onChunk: (chunk: StreamResponse, eventType?: string) => void,
    onComplete?: () => void,
    onError?: (error: string) => void
  ): AbortController => {
    const controller = new AbortController();

    (async () => {
      try {
        const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
        const apiPrefix = `${API_PREFIX}/agent-session`;
        const url = `${baseUrl}${apiPrefix}/chat-completion`;

        const token = localStorage.getItem("access_token");
        const teamId = localStorage.getItem("team_id");

        const requestData = {
          ...data,
          team_id: teamId ? JSON.parse(teamId) : undefined,
        };

        const response = await fetch(url, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
            Accept: "text/event-stream",
            "Cache-Control": "no-cache",
          },
          body: JSON.stringify(requestData),
          signal: controller.signal,
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const reader = response.body?.getReader();
        const decoder = new TextDecoder();

        if (!reader) {
          throw new Error("Response body is not readable");
        }

        let buffer = "";
        let currentEventType: string | undefined = undefined;

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split("\n");
          buffer = lines.pop() || "";

          for (const line of lines) {
            const parsed = parseSSEData(line);
            if (!parsed) {
              if (line.includes("[DONE]")) {
                onComplete?.();
                return;
              }
              continue;
            }
            if (parsed.event) {
              currentEventType = parsed.event;
            } else if (parsed.data) {
              onChunk(parsed.data, currentEventType);
            }
          }
        }

        onComplete?.();
        // queryClient.invalidateQueries({
        //   queryKey: ["sessions", data.session_id, "history"],
        // });
      } catch (error: any) {
        if (!controller.signal.aborted) {
          const errorMessage =
            error instanceof Error ? error.message : "发送消息失败";
          onError?.(errorMessage);
        }
      }
    })();

    return controller;
  };

  return { sendMessage };
};
